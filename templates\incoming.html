{% extends "base.html" %}

{% block title %}الوارد - نظام إدارة الوثائق{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>الوثائق الواردة</h2>
        <a href="{{ url_for('add_incoming_document') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> إضافة وثيقة جديدة
        </a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-0">قائمة الوثائق الواردة</h5>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control" placeholder="بحث...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم الكتاب</th>
                            <th>تاريخ الكتاب</th>
                            <th>الموضوع</th>
                            <th>المرفق</th>
                            <th>تاريخ الإنشاء</th>
                            <th>بواسطة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if documents %}
                            {% for doc in documents %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ doc.document_number }}</td>
                                <td>{{ doc.document_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ doc.subject }}</td>
                                <td>
                                    {% if doc.file_path %}
                                    <a href="{{ url_for('uploaded_file', filename=doc.file_path.split('/')[-1]) }}" class="btn btn-sm btn-primary" target="_blank">
                                        <i class="fas fa-file-pdf"></i> عرض
                                    </a>
                                    {% else %}
                                    <span class="badge bg-secondary">لا يوجد مرفق</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ doc.user.name }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.role == 'admin' or current_user.id == doc.created_by %}
                                        <a href="#" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="8" class="text-center">لا توجد وثائق واردة</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // بحث في الجدول
    document.getElementById('searchInput').addEventListener('keyup', function() {
        let input = this.value.toLowerCase();
        let table = document.querySelector('table');
        let rows = table.getElementsByTagName('tr');
        
        for (let i = 1; i < rows.length; i++) {
            let show = false;
            let cells = rows[i].getElementsByTagName('td');
            
            for (let j = 0; j < cells.length; j++) {
                let cell = cells[j];
                if (cell && cell.textContent.toLowerCase().indexOf(input) > -1) {
                    show = true;
                    break;
                }
            }
            
            rows[i].style.display = show ? '' : 'none';
        }
    });
</script>
{% endblock %}