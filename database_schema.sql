-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS document_management;
USE document_management;

-- إن<PERSON>اء جدول المستخدمين
CREATE TABLE IF NOT EXISTS user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(100) UNIQUE NOT NULL,
    password VARCHAR(200) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الوثائق الصادرة
CREATE TABLE IF NOT EXISTS outgoing_document (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_number VARCHAR(50) NOT NULL,
    document_date DATE NOT NULL,
    subject VARCHAR(200) NOT NULL,
    file_path VARCHAR(255),
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (created_by) REFERENCES user(id)
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الوثائق الواردة
CREATE TABLE IF NOT EXISTS incoming_document (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_number VARCHAR(50) NOT NULL,
    document_date DATE NOT NULL,
    subject VARCHAR(200) NOT NULL,
    file_path VARCHAR(255),
    created_by INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES user(id)
);

-- إدراج بيانات نموذجية للمستخدمين
INSERT INTO user (username, password, name, role) VALUES
('admin', 'pbkdf2:sha256:150000$GJVyGt1Z$8b8936e5b61b62b216c8e5ac649016dcddd779afb103a6f6545f2a0d8bf0b24e', 'المسؤول', 'admin'),
('user1', 'pbkdf2:sha256:150000$RTwWNzMI$66c5f14822c8c7fc1c4c2612a17d328c4f0ac6b94d718af72c83b2d6b91a8876', 'مستخدم 1', 'user'),
('user2', 'pbkdf2:sha256:150000$YMiX7K9J$d57b1ef456d66e7e1a367d8d7c69b62c853fb7b8108793e5f2e0c2ae4e79fd0e', 'مستخدم 2', 'user');

-- إدراج بيانات نموذجية للوثائق الصادرة
INSERT INTO outgoing_document (document_number, document_date, subject, created_by) VALUES
('OUT-2023-001', '2023-01-15', 'طلب توريد معدات مكتبية', 1),
('OUT-2023-002', '2023-02-20', 'دعوة لحضور اجتماع', 1),
('OUT-2023-003', '2023-03-10', 'تقرير شهري', 2);

-- إدراج بيانات نموذجية للوثائق الواردة
INSERT INTO incoming_document (document_number, document_date, subject, created_by) VALUES
('IN-2023-001', '2023-01-10', 'عرض أسعار', 1),
('IN-2023-002', '2023-02-15', 'طلب معلومات', 2),
('IN-2023-003', '2023-03-05', 'شكوى عميل', 1);