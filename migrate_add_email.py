#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database migration script to add email fields to User table
Run this script once to update your existing database
"""

import mysql.connector
from mysql.connector import Error

def migrate_database():
    try:
        # Database connection
        connection = mysql.connector.connect(
            host='localhost',
            database='document_management',
            user='root',
            password=''
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            print("Connected to MySQL database")
            
            # Check if email column already exists
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'document_management' 
                AND TABLE_NAME = 'user' 
                AND COLUMN_NAME = 'email'
            """)
            
            email_exists = cursor.fetchone()[0] > 0
            
            if not email_exists:
                # Add email column
                cursor.execute("""
                    ALTER TABLE user 
                    ADD COLUMN email VARCHAR(120) UNIQUE NULL AFTER username
                """)
                print("✓ Added email column")
            else:
                print("✓ Email column already exists")
            
            # Check if reset_token column already exists
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'document_management' 
                AND TABLE_NAME = 'user' 
                AND COLUMN_NAME = 'reset_token'
            """)
            
            reset_token_exists = cursor.fetchone()[0] > 0
            
            if not reset_token_exists:
                # Add reset_token column
                cursor.execute("""
                    ALTER TABLE user 
                    ADD COLUMN reset_token VARCHAR(100) NULL AFTER role
                """)
                print("✓ Added reset_token column")
            else:
                print("✓ Reset_token column already exists")
            
            # Check if reset_token_expiry column already exists
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'document_management' 
                AND TABLE_NAME = 'user' 
                AND COLUMN_NAME = 'reset_token_expiry'
            """)
            
            reset_expiry_exists = cursor.fetchone()[0] > 0
            
            if not reset_expiry_exists:
                # Add reset_token_expiry column
                cursor.execute("""
                    ALTER TABLE user 
                    ADD COLUMN reset_token_expiry DATETIME NULL AFTER reset_token
                """)
                print("✓ Added reset_token_expiry column")
            else:
                print("✓ Reset_token_expiry column already exists")
            
            connection.commit()
            print("\n✅ Database migration completed successfully!")
            print("\nNext steps:")
            print("1. Install Flask-Mail: pip install flask-mail")
            print("2. Update email configuration in app.py")
            print("3. Test the password reset functionality")
            
    except Error as e:
        print(f"❌ Error: {e}")
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("\nMySQL connection closed")

if __name__ == "__main__":
    print("🔄 Starting database migration...")
    migrate_database()
