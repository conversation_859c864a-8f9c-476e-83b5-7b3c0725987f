from flask import Flask, render_template, request, redirect, url_for, flash, send_from_directory
from flask_sqlalchemy import SQLAlchemy
# Patch werkzeug.security before importing flask_login
import werkzeug.security
from utils_compat import safe_str_cmp
werkzeug.security.safe_str_cmp = safe_str_cmp
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
from datetime import datetime
import uuid
import sqlalchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'مفتاح_سري_للتطبيق'

# إعداد قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+mysqlconnector://root:@localhost/document_management'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# إعداد مجلد لتخزين الملفات المرفوعة
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# تعريف نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # admin, user
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class OutgoingDocument(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_number = db.Column(db.String(50), nullable=False)
    document_date = db.Column(db.Date, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(255), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref='outgoing_documents')

class IncomingDocument(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_number = db.Column(db.String(50), nullable=False)
    document_date = db.Column(db.Date, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(255), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref='incoming_documents')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password, password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور', 'danger')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

# صفحة الصادر
@app.route('/outgoing')
@login_required
def outgoing_documents():
    documents = OutgoingDocument.query.all()
    return render_template('outgoing.html', documents=documents)

# إضافة وثيقة صادرة
@app.route('/outgoing/add', methods=['GET', 'POST'])
@login_required
def add_outgoing_document():
    if request.method == 'POST':
        document_number = request.form.get('document_number')
        document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d')
        subject = request.form.get('subject')
        
        file_path = None
        if 'document_file' in request.files:
            file = request.files['document_file']
            if file.filename != '':
                filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
                file_path = os.path.join('uploads', filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        
        document = OutgoingDocument(
            document_number=document_number,
            document_date=document_date,
            subject=subject,
            file_path=file_path,
            created_by=current_user.id
        )
        
        db.session.add(document)
        db.session.commit()
        
        flash('تمت إضافة الوثيقة بنجاح', 'success')
        return redirect(url_for('outgoing_documents'))
    
    return render_template('add_outgoing.html')

# صفحة الوارد
@app.route('/incoming')
@login_required
def incoming_documents():
    documents = IncomingDocument.query.all()
    return render_template('incoming.html', documents=documents)

# إضافة وثيقة واردة
@app.route('/incoming/add', methods=['GET', 'POST'])
@login_required
def add_incoming_document():
    if request.method == 'POST':
        document_number = request.form.get('document_number')
        document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d')
        subject = request.form.get('subject')
        
        file_path = None
        if 'document_file' in request.files:
            file = request.files['document_file']
            if file.filename != '':
                filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
                file_path = os.path.join('uploads', filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        
        document = IncomingDocument(
            document_number=document_number,
            document_date=document_date,
            subject=subject,
            file_path=file_path,
            created_by=current_user.id
        )
        
        db.session.add(document)
        db.session.commit()
        
        flash('تمت إضافة الوثيقة بنجاح', 'success')
        return redirect(url_for('incoming_documents'))
    
    return render_template('add_incoming.html')

# تحميل الملفات
@app.route('/uploads/<filename>')
@login_required
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# إدارة المستخدمين (للمسؤولين فقط)
@app.route('/users')
@login_required
def users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))
    
    users_list = User.query.all()
    return render_template('users.html', users=users_list)

# إضافة مستخدم جديد
@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        name = request.form.get('name')
        role = request.form.get('role')
        
        # التحقق من وجود المستخدم
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return redirect(url_for('add_user'))
        
        # إنشاء مستخدم جديد
        new_user = User(
            username=username,
            password=generate_password_hash(password),
            name=name,
            role=role
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        flash('تمت إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))
    
    return render_template('add_user.html')

# صفحة حول الموقع
@app.route('/about')
def about():
    return render_template('about.html')

# إنشاء مستخدم مسؤول افتراضي عند بدء التطبيق
@app.before_first_request
def create_admin():
    db.create_all()
    
    # التحقق من وجود مستخدم مسؤول
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            password=generate_password_hash('admin'),
            name='المسؤول',
            role='admin'
        )
        db.session.add(admin)
        db.session.commit()

if __name__ == '__main__':
    app.run(debug=True)