from flask import Flask, render_template, request, redirect, url_for, flash, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail, Message
# Patch werkzeug.security before importing flask_login
import werkzeug.security
from utils_compat import safe_str_cmp
werkzeug.security.safe_str_cmp = safe_str_cmp
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
import uuid
import sqlalchemy
import secrets

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'مفتاح_سري_للتطبيق'

# إعداد قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+mysqlconnector://root:@localhost/document_management'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# إعداد البريد الإلكتروني
try:
    from email_config import *
    app.config['MAIL_SERVER'] = MAIL_SERVER
    app.config['MAIL_PORT'] = MAIL_PORT
    app.config['MAIL_USE_TLS'] = MAIL_USE_TLS
    app.config['MAIL_USERNAME'] = MAIL_USERNAME
    app.config['MAIL_PASSWORD'] = MAIL_PASSWORD
    app.config['MAIL_DEFAULT_SENDER'] = MAIL_DEFAULT_SENDER
except ImportError:
    # إعدادات افتراضية (يجب تغييرها)
    app.config['MAIL_SERVER'] = 'smtp.gmail.com'
    app.config['MAIL_PORT'] = 587
    app.config['MAIL_USE_TLS'] = True
    app.config['MAIL_USERNAME'] = '<EMAIL>'
    app.config['MAIL_PASSWORD'] = 'your-app-password'
    app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
    print("⚠️  تحذير: لم يتم العثور على ملف email_config.py. يرجى إنشاؤه وتعيين إعدادات البريد الإلكتروني")

mail = Mail(app)

# إعداد مجلد لتخزين الملفات المرفوعة
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# تعريف نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # admin, user
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Temporary: These will be added to database later
    @property
    def email(self):
        return getattr(self, '_email', None)

    @email.setter
    def email(self, value):
        self._email = value

    @property
    def reset_token(self):
        return getattr(self, '_reset_token', None)

    @reset_token.setter
    def reset_token(self, value):
        self._reset_token = value

    @property
    def reset_token_expiry(self):
        return getattr(self, '_reset_token_expiry', None)

    @reset_token_expiry.setter
    def reset_token_expiry(self, value):
        self._reset_token_expiry = value

    def generate_reset_token(self):
        """إنشاء رمز إعادة تعيين كلمة المرور"""
        # Temporary: Store in session/memory until database is updated
        token = secrets.token_urlsafe(32)
        expiry = datetime.utcnow() + timedelta(hours=1)

        # Store in a temporary global dict (not recommended for production)
        if not hasattr(app, 'reset_tokens'):
            app.reset_tokens = {}

        app.reset_tokens[token] = {
            'user_id': self.id,
            'expiry': expiry
        }

        return token

    def verify_reset_token(self, token):
        """التحقق من صحة رمز إعادة تعيين كلمة المرور"""
        if not hasattr(app, 'reset_tokens'):
            return False

        token_data = app.reset_tokens.get(token)
        if (token_data and
            token_data['user_id'] == self.id and
            datetime.utcnow() < token_data['expiry']):
            return True
        return False

    def clear_reset_token(self):
        """مسح رمز إعادة تعيين كلمة المرور"""
        # Remove all tokens for this user
        if hasattr(app, 'reset_tokens'):
            tokens_to_remove = []
            for token, data in app.reset_tokens.items():
                if data['user_id'] == self.id:
                    tokens_to_remove.append(token)

            for token in tokens_to_remove:
                del app.reset_tokens[token]

class OutgoingDocument(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_number = db.Column(db.String(50), nullable=False)
    document_date = db.Column(db.Date, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(255), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref='outgoing_documents')

class IncomingDocument(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_number = db.Column(db.String(50), nullable=False)
    document_date = db.Column(db.Date, nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(255), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user = db.relationship('User', backref='incoming_documents')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور', 'danger')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

# صفحة الصادر
@app.route('/outgoing')
@login_required
def outgoing_documents():
    documents = OutgoingDocument.query.all()
    return render_template('outgoing.html', documents=documents)

# إضافة وثيقة صادرة
@app.route('/outgoing/add', methods=['GET', 'POST'])
@login_required
def add_outgoing_document():
    if request.method == 'POST':
        document_number = request.form.get('document_number')
        document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d')
        subject = request.form.get('subject')

        file_path = None
        if 'document_file' in request.files:
            file = request.files['document_file']
            if file.filename != '':
                filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
                file_path = os.path.join('uploads', filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))

        document = OutgoingDocument(
            document_number=document_number,
            document_date=document_date,
            subject=subject,
            file_path=file_path,
            created_by=current_user.id
        )

        db.session.add(document)
        db.session.commit()

        flash('تمت إضافة الوثيقة بنجاح', 'success')
        return redirect(url_for('outgoing_documents'))

    return render_template('add_outgoing.html')

# صفحة الوارد
@app.route('/incoming')
@login_required
def incoming_documents():
    documents = IncomingDocument.query.all()
    return render_template('incoming.html', documents=documents)

# إضافة وثيقة واردة
@app.route('/incoming/add', methods=['GET', 'POST'])
@login_required
def add_incoming_document():
    if request.method == 'POST':
        document_number = request.form.get('document_number')
        document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d')
        subject = request.form.get('subject')

        file_path = None
        if 'document_file' in request.files:
            file = request.files['document_file']
            if file.filename != '':
                filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
                file_path = os.path.join('uploads', filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))

        document = IncomingDocument(
            document_number=document_number,
            document_date=document_date,
            subject=subject,
            file_path=file_path,
            created_by=current_user.id
        )

        db.session.add(document)
        db.session.commit()

        flash('تمت إضافة الوثيقة بنجاح', 'success')
        return redirect(url_for('incoming_documents'))

    return render_template('add_incoming.html')

# تحميل الملفات
@app.route('/uploads/<filename>')
@login_required
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# إدارة المستخدمين (للمسؤولين فقط)
@app.route('/users')
@login_required
def users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    users_list = User.query.all()
    return render_template('users.html', users=users_list)

# إضافة مستخدم جديد
@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        name = request.form.get('name')
        role = request.form.get('role')

        # التحقق من وجود المستخدم
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return redirect(url_for('add_user'))

        # التحقق من عدم وجود بريد إلكتروني مكرر
        if email:
            existing_email = User.query.filter_by(email=email).first()
            if existing_email:
                flash('البريد الإلكتروني مستخدم بالفعل', 'danger')
                return redirect(url_for('add_user'))

        # إنشاء مستخدم جديد
        new_user = User(
            username=username,
            email=email if email else None,
            password=generate_password_hash(password),
            name=name,
            role=role
        )

        db.session.add(new_user)
        db.session.commit()

        flash('تمت إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('add_user.html')

# نسيان كلمة المرور
@app.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form.get('email')
        user = User.query.filter_by(email=email).first()

        if user:
            # إنشاء رمز إعادة تعيين
            token = user.generate_reset_token()

            # إرسال بريد إلكتروني
            try:
                msg = Message('إعادة تعيين كلمة المرور - نظام إدارة الوثائق',
                              recipients=[email])

                reset_url = url_for('reset_password', token=token, _external=True)
                msg.html = render_template('email/reset_password.html',
                                         user=user, reset_url=reset_url)

                mail.send(msg)
                flash('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success')
            except Exception as e:
                flash('حدث خطأ في إرسال البريد الإلكتروني. يرجى المحاولة لاحقاً', 'danger')
        else:
            flash('لا يوجد مستخدم بهذا البريد الإلكتروني', 'danger')

        return redirect(url_for('forgot_password'))

    return render_template('forgot_password.html')

# إعادة تعيين كلمة المرور
@app.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    # البحث عن المستخدم باستخدام الرمز (مؤقت)
    user = None
    if hasattr(app, 'reset_tokens') and token in app.reset_tokens:
        token_data = app.reset_tokens[token]
        if datetime.utcnow() < token_data['expiry']:
            user = User.query.get(token_data['user_id'])

    if not user or not user.verify_reset_token(token):
        flash('رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية', 'danger')
        return redirect(url_for('login'))

    if request.method == 'POST':
        new_password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if new_password != confirm_password:
            flash('كلمتا المرور غير متطابقتين', 'danger')
            return render_template('reset_password.html', token=token)

        if len(new_password) < 6:
            flash('يجب أن تكون كلمة المرور 6 أحرف على الأقل', 'danger')
            return render_template('reset_password.html', token=token)

        # تحديث كلمة المرور
        user.password = generate_password_hash(new_password)
        user.clear_reset_token()

        flash('تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول', 'success')
        return redirect(url_for('login'))

    return render_template('reset_password.html', token=token)

# صفحة حول الموقع
@app.route('/about')
def about():
    return render_template('about.html')

# إنشاء مستخدم مسؤول افتراضي عند بدء التطبيق
@app.before_first_request
def create_admin():
    db.create_all()

    # التحقق من وجود مستخدم مسؤول
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            password=generate_password_hash('admin'),
            name='المسؤول',
            role='admin'
        )
        db.session.add(admin)
        db.session.commit()

if __name__ == '__main__':
    app.run(debug=True)