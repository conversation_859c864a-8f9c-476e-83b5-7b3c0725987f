{% extends "base.html" %}

{% block title %}إضافة وثيقة صادرة - نظام إدارة الوثائق{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>إضافة وثيقة صادرة جديدة</h2>
        <a href="{{ url_for('outgoing_documents') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">بيانات الوثيقة</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('add_outgoing_document') }}" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="document_number" class="form-label">رقم الكتاب <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="document_number" name="document_number" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="document_date" class="form-label">تاريخ الكتاب <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="document_date" name="document_date" required>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="subject" name="subject" rows="3" required></textarea>
                </div>
                <div class="mb-3">
                    <label for="document_file" class="form-label">المرفق (PDF)</label>
                    <input type="file" class="form-control" id="document_file" name="document_file" accept=".pdf">
                    <div class="form-text">يرجى رفع ملف بصيغة PDF فقط.</div>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="reset" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-redo me-1"></i> إعادة تعيين
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تعيين تاريخ اليوم كقيمة افتراضية لحقل التاريخ
    document.addEventListener('DOMContentLoaded', function() {
        let today = new Date().toISOString().split('T')[0];
        document.getElementById('document_date').value = today;
    });
</script>
{% endblock %}