import mysql.connector
from mysql.connector import errorcode
import os

# Database connection setup
def setup_database():
    try:
        # Create connection to server
        cnx = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        cursor = cnx.cursor()
        
        # Create database if it doesn't exist
        cursor.execute("CREATE DATABASE IF NOT EXISTS document_management")
        cursor.execute("USE document_management")
        
        print("Database created successfully!")
        
        # Close connection
        cursor.close()
        cnx.close()
        
        print("Database setup completed successfully!")
        return True
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            print("Error in username or password")
        else:
            print(f"Error: {err}")
        return False

# Create folder for uploaded files
def create_upload_folder():
    upload_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
        print("Upload folder created successfully!")
    else:
        print("Upload folder already exists!")

if __name__ == "__main__":
    print("Starting database setup...")
    setup_database()
    create_upload_folder()
    print("Setup completed!")
    print("You can now run the application using the command: python app.py")