#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for password reset functionality
Run this after setting up the email configuration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, mail
from flask_mail import Message

def test_email_config():
    """Test email configuration"""
    print("🔧 Testing email configuration...")
    
    with app.app_context():
        try:
            # Test mail configuration
            print(f"Mail Server: {app.config.get('MAIL_SERVER')}")
            print(f"Mail Port: {app.config.get('MAIL_PORT')}")
            print(f"Mail Username: {app.config.get('MAIL_USERNAME')}")
            print(f"Mail Default Sender: {app.config.get('MAIL_DEFAULT_SENDER')}")
            
            if app.config.get('MAIL_USERNAME') == '<EMAIL>':
                print("⚠️  Warning: Default email configuration detected. Please update email_config.py")
                return False
            
            print("✅ Email configuration looks good!")
            return True
            
        except Exception as e:
            print(f"❌ Email configuration error: {e}")
            return False

def test_user_model():
    """Test User model with new fields"""
    print("\n👤 Testing User model...")
    
    with app.app_context():
        try:
            # Test creating a user with email
            test_user = User(
                username='test_user_temp',
                email='<EMAIL>',
                password='test_password',
                name='Test User',
                role='user'
            )
            
            # Test token generation
            token = test_user.generate_reset_token()
            print(f"✅ Generated reset token: {token[:20]}...")
            
            # Test token verification
            is_valid = test_user.verify_reset_token(token)
            print(f"✅ Token verification: {is_valid}")
            
            # Test token clearing
            test_user.clear_reset_token()
            print("✅ Token cleared successfully")
            
            return True
            
        except Exception as e:
            print(f"❌ User model error: {e}")
            return False

def test_database_schema():
    """Test database schema for new columns"""
    print("\n🗄️  Testing database schema...")
    
    with app.app_context():
        try:
            # Check if we can query users with email field
            users = User.query.all()
            
            for user in users[:3]:  # Test first 3 users
                print(f"User: {user.username}, Email: {user.email or 'Not set'}")
            
            print("✅ Database schema is correct!")
            return True
            
        except Exception as e:
            print(f"❌ Database schema error: {e}")
            print("💡 Hint: Run 'python migrate_add_email.py' to update the database")
            return False

def test_send_email():
    """Test sending a test email"""
    print("\n📧 Testing email sending...")
    
    with app.app_context():
        try:
            if app.config.get('MAIL_USERNAME') == '<EMAIL>':
                print("⚠️  Skipping email test - please configure email settings first")
                return False
            
            # Create a test message
            msg = Message(
                'Test Email - نظام إدارة الوثائق',
                recipients=[app.config.get('MAIL_USERNAME')]  # Send to self
            )
            msg.html = """
            <h2>Test Email</h2>
            <p>This is a test email from the Document Management System.</p>
            <p>If you receive this, email configuration is working correctly!</p>
            <p>هذا بريد إلكتروني تجريبي من نظام إدارة الوثائق</p>
            """
            
            mail.send(msg)
            print("✅ Test email sent successfully!")
            print(f"📬 Check your inbox: {app.config.get('MAIL_USERNAME')}")
            return True
            
        except Exception as e:
            print(f"❌ Email sending error: {e}")
            print("💡 Hints:")
            print("   - Check your email configuration in email_config.py")
            print("   - Make sure you're using an App Password for Gmail")
            print("   - Check your internet connection")
            return False

def main():
    """Run all tests"""
    print("🧪 Password Reset System Test")
    print("=" * 50)
    
    tests = [
        test_email_config,
        test_database_schema,
        test_user_model,
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    # Optional email test
    print("\n📧 Email Test (Optional)")
    print("-" * 30)
    send_test = input("Do you want to send a test email? (y/N): ").lower().strip()
    if send_test == 'y':
        email_result = test_send_email()
        results.append(email_result)
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All tests passed! ({passed}/{total})")
        print("\n🎉 Password reset system is ready to use!")
        print("\nNext steps:")
        print("1. Add email addresses to existing users")
        print("2. Test the forgot password feature in the web interface")
        print("3. Update email_config.py with your actual email settings")
    else:
        print(f"⚠️  Some tests failed ({passed}/{total})")
        print("\n🔧 Please fix the issues above before using the password reset system")

if __name__ == "__main__":
    main()
