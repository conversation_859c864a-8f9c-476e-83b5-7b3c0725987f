#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple database migration script
"""

try:
    import mysql.connector
    from mysql.connector import Error
    
    print("🔄 Starting database migration...")
    
    # Database connection
    connection = mysql.connector.connect(
        host='localhost',
        database='document_management',
        user='root',
        password=''
    )
    
    if connection.is_connected():
        cursor = connection.cursor()
        print("✅ Connected to MySQL database")
        
        # Add email column
        try:
            cursor.execute("ALTER TABLE user ADD COLUMN email VARCHAR(120) UNIQUE NULL AFTER username")
            print("✅ Added email column")
        except Error as e:
            if "Duplicate column name" in str(e):
                print("✅ Email column already exists")
            else:
                print(f"❌ Error adding email column: {e}")
        
        # Add reset_token column
        try:
            cursor.execute("ALTER TABLE user ADD COLUMN reset_token VARCHAR(100) NULL AFTER role")
            print("✅ Added reset_token column")
        except Error as e:
            if "Duplicate column name" in str(e):
                print("✅ Reset_token column already exists")
            else:
                print(f"❌ Error adding reset_token column: {e}")
        
        # Add reset_token_expiry column
        try:
            cursor.execute("ALTER TABLE user ADD COLUMN reset_token_expiry DATETIME NULL AFTER reset_token")
            print("✅ Added reset_token_expiry column")
        except Error as e:
            if "Duplicate column name" in str(e):
                print("✅ Reset_token_expiry column already exists")
            else:
                print(f"❌ Error adding reset_token_expiry column: {e}")
        
        connection.commit()
        
        # Show table structure
        cursor.execute("DESCRIBE user")
        columns = cursor.fetchall()
        print("\n📋 Current user table structure:")
        for column in columns:
            print(f"   {column[0]} - {column[1]}")
        
        print("\n🎉 Migration completed successfully!")
        
        cursor.close()
        connection.close()
        
except ImportError:
    print("❌ mysql-connector-python not installed")
    print("💡 Install it with: pip install mysql-connector-python")
except Error as e:
    print(f"❌ Database error: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
