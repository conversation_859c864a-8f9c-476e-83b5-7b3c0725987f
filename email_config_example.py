# -*- coding: utf-8 -*-
"""
Email Configuration Example
Copy this file to email_config.py and update with your email settings
"""

# Gmail Configuration (recommended)
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'  # Your Gmail address
MAIL_PASSWORD = 'your-app-password'     # Gmail App Password (not your regular password)
MAIL_DEFAULT_SENDER = '<EMAIL>'

# Alternative: Outlook/Hotmail Configuration
# MAIL_SERVER = 'smtp-mail.outlook.com'
# MAIL_PORT = 587
# MAIL_USE_TLS = True
# MAIL_USERNAME = '<EMAIL>'
# MAIL_PASSWORD = 'your-password'
# MAIL_DEFAULT_SENDER = '<EMAIL>'

# Alternative: Yahoo Configuration
# MAIL_SERVER = 'smtp.mail.yahoo.com'
# MAIL_PORT = 587
# MAIL_USE_TLS = True
# MAIL_USERNAME = '<EMAIL>'
# MAIL_PASSWORD = 'your-app-password'
# MAIL_DEFAULT_SENDER = '<EMAIL>'

"""
Instructions for Gmail App Password:
1. Enable 2-Factor Authentication on your Google account
2. Go to Google Account settings > Security > App passwords
3. Generate a new app password for "Mail"
4. Use this app password instead of your regular password
"""
