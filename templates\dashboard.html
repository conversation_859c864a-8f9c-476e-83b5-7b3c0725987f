{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الوثائق{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>لوحة التحكم</h2>
        <div>
            <span class="badge bg-primary">{{ current_user.role }}</span>
            <span class="ms-2">مرحباً، {{ current_user.name }}</span>
        </div>
    </div>

    <div class="row">
        <!-- إحصائيات الصادر -->
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الصادر</h6>
                            <h2 class="mb-0">{{ outgoing_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-paper-plane fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span>عرض التفاصيل</span>
                    <a href="{{ url_for('outgoing_documents') }}" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات الوارد -->
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الوارد</h6>
                            <h2 class="mb-0">{{ incoming_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-inbox fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span>عرض التفاصيل</span>
                    <a href="{{ url_for('incoming_documents') }}" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        {% if current_user.role == 'admin' %}
        <!-- إحصائيات المستخدمين -->
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">المستخدمين</h6>
                            <h2 class="mb-0">{{ users_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-users fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span>إدارة المستخدمين</span>
                    <a href="{{ url_for('users') }}" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- روابط سريعة -->
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card text-white bg-secondary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">روابط سريعة</h6>
                            <p class="mb-0">الوصول السريع</p>
                        </div>
                        <i class="fas fa-link fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center">
                    <span>حول النظام</span>
                    <a href="{{ url_for('about') }}" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- آخر الوثائق الصادرة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">آخر الوثائق الصادرة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الكتاب</th>
                                    <th>التاريخ</th>
                                    <th>الموضوع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_outgoing %}
                                    {% for doc in recent_outgoing %}
                                    <tr>
                                        <td>{{ doc.document_number }}</td>
                                        <td>{{ doc.document_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ doc.subject }}</td>
                                        <td>
                                            {% if doc.file_path %}
                                            <a href="{{ url_for('uploaded_file', filename=doc.file_path.split('/')[-1]) }}" class="btn btn-sm btn-primary" target="_blank">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">لا توجد وثائق صادرة</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ url_for('outgoing_documents') }}" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
            </div>
        </div>

        <!-- آخر الوثائق الواردة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">آخر الوثائق الواردة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الكتاب</th>
                                    <th>التاريخ</th>
                                    <th>الموضوع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if recent_incoming %}
                                    {% for doc in recent_incoming %}
                                    <tr>
                                        <td>{{ doc.document_number }}</td>
                                        <td>{{ doc.document_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ doc.subject }}</td>
                                        <td>
                                            {% if doc.file_path %}
                                            <a href="{{ url_for('uploaded_file', filename=doc.file_path.split('/')[-1]) }}" class="btn btn-sm btn-primary" target="_blank">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="4" class="text-center">لا توجد وثائق واردة</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ url_for('incoming_documents') }}" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // يمكن إضافة أي سكربت إضافي هنا
</script>
{% endblock %}