# إعداد نظام إعادة تعيين كلمة المرور

## المميزات الجديدة

تم إضافة نظام شامل لإعادة تعيين كلمة المرور يتضمن:

- ✅ إعادة تعيين كلمة المرور عبر البريد الإلكتروني
- ✅ رموز آمنة مع انتهاء صلاحية (ساعة واحدة)
- ✅ قوالب بريد إلكتروني باللغة العربية
- ✅ واجهة مستخدم متجاوبة
- ✅ التحقق من صحة البيانات
- ✅ حماية من الهجمات الأمنية

## خطوات التثبيت

### 1. تثبيت المتطلبات الجديدة

```bash
pip install flask-mail
```

أو تثبيت جميع المتطلبات:

```bash
pip install -r requirements.txt
```

### 2. تحديث قاعدة البيانات

قم بتشغيل سكريبت الترحيل لإضافة الحقول الجديدة:

```bash
python migrate_add_email.py
```

### 3. إعداد البريد الإلكتروني

#### أ. إنشاء ملف الإعدادات

انسخ ملف المثال وقم بتعديله:

```bash
cp email_config_example.py email_config.py
```

#### ب. تعديل إعدادات Gmail

1. فعّل المصادقة الثنائية على حساب Google
2. اذهب إلى إعدادات الحساب > الأمان > كلمات مرور التطبيقات
3. أنشئ كلمة مرور تطبيق جديدة للبريد
4. استخدم كلمة مرور التطبيق في الملف

```python
# email_config.py
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-16-digit-app-password'
MAIL_DEFAULT_SENDER = '<EMAIL>'
```

### 4. إضافة بريد إلكتروني للمستخدمين

#### للمستخدمين الجدد:
- سيظهر حقل البريد الإلكتروني في نموذج إضافة المستخدم

#### للمستخدمين الحاليين:
قم بتحديث قاعدة البيانات مباشرة:

```sql
UPDATE user SET email = '<EMAIL>' WHERE username = 'admin';
```

## كيفية الاستخدام

### 1. نسيان كلمة المرور

1. في صفحة تسجيل الدخول، انقر على "نسيت كلمة المرور؟"
2. أدخل البريد الإلكتروني المسجل
3. ستصل رسالة بريد إلكتروني تحتوي على رابط إعادة التعيين
4. انقر على الرابط (صالح لمدة ساعة واحدة)
5. أدخل كلمة المرور الجديدة

### 2. إدارة المستخدمين

- يمكن للمسؤولين رؤية البريد الإلكتروني في قائمة المستخدمين
- عند إضافة مستخدم جديد، يمكن إدخال البريد الإلكتروني (اختياري)

## الملفات الجديدة

```
templates/
├── forgot_password.html      # نموذج طلب إعادة التعيين
├── reset_password.html       # نموذج تعيين كلمة مرور جديدة
└── email/
    └── reset_password.html   # قالب البريد الإلكتروني

migrate_add_email.py          # سكريبت ترحيل قاعدة البيانات
email_config_example.py       # مثال على إعدادات البريد
email_config.py              # إعدادات البريد (يجب إنشاؤه)
```

## الملفات المحدثة

- `app.py` - إضافة routes جديدة وتحديث User model
- `templates/login.html` - إضافة رابط "نسيت كلمة المرور"
- `templates/add_user.html` - إضافة حقل البريد الإلكتروني
- `templates/users.html` - عرض البريد الإلكتروني في الجدول
- `requirements.txt` - إضافة flask-mail

## الأمان

- الرموز آمنة ومشفرة
- انتهاء صلاحية تلقائي (ساعة واحدة)
- التحقق من صحة البريد الإلكتروني
- حماية من إعادة استخدام الرموز
- رسائل خطأ آمنة

## استكشاف الأخطاء

### خطأ في إرسال البريد الإلكتروني

1. تأكد من صحة إعدادات البريد في `email_config.py`
2. تأكد من تفعيل المصادقة الثنائية وإنشاء كلمة مرور التطبيق
3. تحقق من اتصال الإنترنت
4. تأكد من عدم حظر Gmail للتطبيق

### المستخدم لا يملك بريد إلكتروني

```sql
-- إضافة بريد إلكتروني لمستخدم موجود
UPDATE user SET email = '<EMAIL>' WHERE username = 'username';
```

### خطأ في قاعدة البيانات

```bash
# إعادة تشغيل سكريبت الترحيل
python migrate_add_email.py
```

## الدعم

للحصول على المساعدة:
1. تحقق من رسائل الخطأ في وحدة التحكم
2. تأكد من تشغيل جميع خطوات التثبيت
3. راجع إعدادات البريد الإلكتروني

---

**ملاحظة**: تأكد من الاحتفاظ بنسخة احتياطية من قاعدة البيانات قبل تشغيل سكريبت الترحيل.
