import mysql.connector
import os

# Database connection setup
def import_data():
    try:
        # Create connection to server
        cnx = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="document_management"
        )
        cursor = cnx.cursor()
        
        # Read SQL file
        sql_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'database_schema.sql')
        
        with open(sql_file_path, 'r', encoding='utf-8') as sql_file:
            sql_script = sql_file.read()
        
        # Execute SQL commands
        for statement in sql_script.split(';'):
            if statement.strip():
                cursor.execute(statement)
        
        # Commit changes
        cnx.commit()
        
        # Close connection
        cursor.close()
        cnx.close()
        
        print("Data imported successfully!")
        return True
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return False

if __name__ == "__main__":
    print("Starting data import...")
    import_data()
    print("Import completed!")