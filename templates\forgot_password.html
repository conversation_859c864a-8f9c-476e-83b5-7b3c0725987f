{% extends "base.html" %}

{% block title %}نسيان كلمة المرور - نظام إدارة الوثائق{% endblock %}

{% block auth_content %}
<div class="container login-container">
    <div class="card">
        <div class="card-header bg-warning text-white text-center">
            <h3>نسيان كلمة المرور</h3>
        </div>
        <div class="card-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <p class="text-muted mb-4">أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور</p>

            <form method="POST" action="{{ url_for('forgot_password') }}">
                <div class="mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="email" name="email" required 
                           placeholder="أدخل بريدك الإلكتروني">
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-paper-plane me-1"></i> إرسال رابط إعادة التعيين
                    </button>
                </div>
            </form>
        </div>
        <div class="card-footer text-center">
            <a href="{{ url_for('login') }}" class="text-decoration-none">
                <i class="fas fa-arrow-right me-1"></i> العودة لتسجيل الدخول
            </a>
        </div>
    </div>
</div>
{% endblock %}
