-- SQL script to add email columns to the user table
-- Run this in your MySQL database

USE document_management;

-- Add email column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'document_management' 
     AND TABLE_NAME = 'user' 
     AND COLUMN_NAME = 'email') = 0,
    'ALTER TABLE user ADD COLUMN email VARCHAR(120) UNIQUE NULL AFTER username',
    'SELECT "Email column already exists" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add reset_token column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'document_management' 
     AND TABLE_NAME = 'user' 
     AND COLUMN_NAME = 'reset_token') = 0,
    'ALTER TABLE user ADD COLUMN reset_token VARCHAR(100) NULL AFTER role',
    'SELECT "Reset_token column already exists" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add reset_token_expiry column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'document_management' 
     AND TABLE_NAME = 'user' 
     AND COLUMN_NAME = 'reset_token_expiry') = 0,
    'ALTER TABLE user ADD COLUMN reset_token_expiry DATETIME NULL AFTER reset_token',
    'SELECT "Reset_token_expiry column already exists" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the updated table structure
DESCRIBE user;

-- Show success message
SELECT 'Database migration completed successfully!' as status;
