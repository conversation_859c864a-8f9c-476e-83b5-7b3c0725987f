# Compatibility utilities for werkzeug and flask_login

def safe_str_cmp(a, b):
    """
    This function compares strings in somewhat constant time. This
    requires that the length of at least one string is known in advance.
    
    Returns `True` if the two strings are equal, or `False` if they are not.
    """
    if isinstance(a, str):
        a = a.encode('utf-8')
    if isinstance(b, str):
        b = b.encode('utf-8')

    if len(a) != len(b):
        return False

    result = 0
    for x, y in zip(a, b):
        result |= x ^ y
    return result == 0