{% extends "base.html" %}

{% block title %}إعادة تعيين كلمة المرور - نظام إدارة الوثائق{% endblock %}

{% block auth_content %}
<div class="container login-container">
    <div class="card">
        <div class="card-header bg-success text-white text-center">
            <h3>إعادة تعيين كلمة المرور</h3>
        </div>
        <div class="card-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <p class="text-muted mb-4">أدخل كلمة المرور الجديدة</p>

            <form method="POST" action="{{ url_for('reset_password', token=token) }}">
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="password" name="password" required 
                           placeholder="أدخل كلمة المرور الجديدة" minlength="6">
                    <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                </div>
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                           placeholder="أعد إدخال كلمة المرور" minlength="6">
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i> حفظ كلمة المرور الجديدة
                    </button>
                </div>
            </form>
        </div>
        <div class="card-footer text-center">
            <a href="{{ url_for('login') }}" class="text-decoration-none">
                <i class="fas fa-arrow-right me-1"></i> العودة لتسجيل الدخول
            </a>
        </div>
    </div>
</div>

<script>
// التحقق من تطابق كلمتي المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمتا المرور غير متطابقتين');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
